/**
 * Bidirectional Type Compatibility Module
 *
 * Provides comprehensive field type compatibility checking and validation for
 * bidirectional synchronization between AutoPatient (AP) and CliniCore (CC) systems.
 *
 * Features:
 * - Enhanced field type compatibility matrix
 * - Graceful handling of mismatched but compatible types
 * - Validation functions for field definitions
 * - Support for PHONE/telephone, TEXT/textarea, boolean/radio conversions
 * - Production-ready error handling and logging
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logWarn } from "@/utils/logger";

/**
 * AP to CC field type mapping for bidirectional conversion
 */
export const AP_TO_CC_TYPE_MAPPING: Record<string, string> = {
	// Text fields
	TEXT: "text",
	TEXTAREA: "textarea",
	
	// Selection fields
	RADIO: "select", // Will be overridden to "boolean" for Yes/No fields
	MULTIPLE_OPTIONS: "multiselect",
	
	// Input fields
	PHONE: "phone",
	EMAIL: "email",
	NUMBER: "number",
	
	// Date and time
	DATE: "date",
	TIME: "time",
	DATETIME: "datetime",
	
	// File fields
	FILE_UPLOAD: "file",
	
	// Special fields
	SIGNATURE: "signature",
	
	// Default fallback
	default: "text",
};

/**
 * CC to AP field type mapping for bidirectional conversion
 */
export const CC_TO_AP_TYPE_MAPPING: Record<string, string> = {
	// Text fields
	text: "TEXT",
	textarea: "TEXTAREA",
	string: "TEXT",
	varchar: "TEXT",
	
	// Selection fields
	boolean: "RADIO", // Will have Yes/No options
	select: "RADIO",
	multiselect: "MULTIPLE_OPTIONS",
	checkbox: "MULTIPLE_OPTIONS",
	radio: "RADIO",
	
	// Input fields
	phone: "PHONE",
	telephone: "PHONE", // Handle telephone/phone mismatch
	email: "EMAIL",
	number: "NUMBER",
	decimal: "NUMBER",
	float: "NUMBER",
	integer: "NUMBER",
	
	// Date and time
	date: "DATE",
	time: "TIME",
	datetime: "DATETIME",
	timestamp: "DATETIME",
	
	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",
	
	// Special fields
	signature: "SIGNATURE",
	
	// Default fallback
	default: "TEXT",
};

/**
 * Field type compatibility matrix for mismatched but compatible types
 * Each key represents a field type, and the array contains compatible types
 */
export const TYPE_COMPATIBILITY_MATRIX: Record<string, string[]> = {
	// Text field compatibility
	text: ["textarea", "string", "varchar", "email", "phone", "telephone"],
	textarea: ["text", "string", "varchar"],
	string: ["text", "textarea", "varchar"],
	varchar: ["text", "textarea", "string"],
	
	// Phone field compatibility (handle PHONE/telephone mismatch)
	phone: ["telephone", "text", "string", "varchar"],
	telephone: ["phone", "text", "string", "varchar"],
	
	// Boolean field compatibility (handle boolean/radio mismatch)
	boolean: ["select", "radio", "text"],
	select: ["boolean", "radio", "text", "textarea"],
	radio: ["boolean", "select", "text"],
	
	// Number field compatibility
	number: ["decimal", "float", "integer", "text", "string"],
	decimal: ["number", "float", "text", "string"],
	float: ["number", "decimal", "text", "string"],
	integer: ["number", "text", "string"],
	
	// Multi-value field compatibility
	multiselect: ["checkbox", "text", "textarea"],
	checkbox: ["multiselect", "text", "textarea"],
	
	// Email field compatibility
	email: ["text", "string", "varchar"],
	
	// Date field compatibility
	date: ["datetime", "timestamp", "text", "string"],
	datetime: ["date", "timestamp", "text", "string"],
	timestamp: ["date", "datetime", "text", "string"],
	time: ["text", "string"],
	
	// File field compatibility
	file: ["upload", "attachment", "text"],
	upload: ["file", "attachment", "text"],
	attachment: ["file", "upload", "text"],
};

/**
 * Check if two field types are compatible for conversion
 *
 * @param sourceType - Source field type (normalized to lowercase)
 * @param targetType - Target field type (normalized to lowercase)
 * @returns true if types are compatible for conversion
 */
export function areTypesCompatible(sourceType: string, targetType: string): boolean {
	const normalizedSource = sourceType.toLowerCase().trim();
	const normalizedTarget = targetType.toLowerCase().trim();
	
	// Direct type match
	if (normalizedSource === normalizedTarget) {
		return true;
	}
	
	// Check compatibility matrix
	const compatibleTypes = TYPE_COMPATIBILITY_MATRIX[normalizedSource] || [];
	return compatibleTypes.includes(normalizedTarget);
}

/**
 * Validate AP to CC field type compatibility
 *
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns true if fields are compatible for conversion
 */
export function validateApToCcCompatibility(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): boolean {
	const expectedCcType = getExpectedCcType(apField);
	const actualCcType = ccField.type.toLowerCase().trim();
	
	// Check direct compatibility
	if (areTypesCompatible(expectedCcType, actualCcType)) {
		logDebug(
			requestId,
			`Field types compatible: AP ${apField.dataType} → CC ${ccField.type} for field "${apField.name}"`,
		);
		return true;
	}
	
	// Log compatibility warning but allow conversion
	logWarn(
		requestId,
		`Type compatibility warning: AP ${apField.dataType} → CC ${ccField.type} for field "${apField.name}". Conversion will continue with potential data transformation.`,
	);
	
	return true; // Allow conversion with warning (graceful handling)
}

/**
 * Validate CC to AP field type compatibility
 *
 * @param ccField - CC custom field definition
 * @param apField - AP custom field definition
 * @param requestId - Request ID for logging
 * @returns true if fields are compatible for conversion
 */
export function validateCcToApCompatibility(
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): boolean {
	const expectedApType = getExpectedApType(ccField);
	const actualApType = apField.dataType.toLowerCase().trim();
	
	// Check direct compatibility
	if (areTypesCompatible(expectedApType.toLowerCase(), actualApType)) {
		logDebug(
			requestId,
			`Field types compatible: CC ${ccField.type} → AP ${apField.dataType} for field "${ccField.name}"`,
		);
		return true;
	}
	
	// Log compatibility warning but allow conversion
	logWarn(
		requestId,
		`Type compatibility warning: CC ${ccField.type} → AP ${apField.dataType} for field "${ccField.name}". Conversion will continue with potential data transformation.`,
	);
	
	return true; // Allow conversion with warning (graceful handling)
}

/**
 * Get expected CC field type for an AP field
 *
 * @param apField - AP custom field definition
 * @returns Expected CC field type
 */
export function getExpectedCcType(apField: APGetCustomFieldType): string {
	// Special handling for boolean RADIO fields
	if (isApBooleanRadioField(apField)) {
		return "boolean";
	}
	
	// Use standard mapping
	return AP_TO_CC_TYPE_MAPPING[apField.dataType] || AP_TO_CC_TYPE_MAPPING.default;
}

/**
 * Get expected AP field type for a CC field
 *
 * @param ccField - CC custom field definition
 * @returns Expected AP field type
 */
export function getExpectedApType(ccField: GetCCCustomField): string {
	const ccType = ccField.type.toLowerCase().trim();
	
	// Special handling for select fields with multiple values
	if (ccType === "select" && ccField.allowMultipleValues === true) {
		return "MULTIPLE_OPTIONS";
	}
	
	// Use standard mapping
	return CC_TO_AP_TYPE_MAPPING[ccType] || CC_TO_AP_TYPE_MAPPING.default;
}

/**
 * Check if an AP field is a boolean RADIO field (Yes/No options)
 *
 * @param apField - AP custom field definition
 * @returns true if field is a boolean RADIO field
 */
export function isApBooleanRadioField(apField: APGetCustomFieldType): boolean {
	if (apField.dataType !== "RADIO") {
		return false;
	}
	
	// Check if field has Yes/No options (from picklistOptions or textBoxListOptions)
	const options = (apField as any).picklistOptions || 
		(apField.textBoxListOptions?.map(opt => opt.label)) || [];
	
	if (options.length !== 2) {
		return false;
	}
	
	const normalizedOptions = options.map((opt: string) => opt.toLowerCase().trim());
	return normalizedOptions.includes("yes") && normalizedOptions.includes("no");
}

/**
 * Check if a CC field is a boolean field
 *
 * @param ccField - CC custom field definition
 * @returns true if field is a boolean field
 */
export function isCcBooleanField(ccField: GetCCCustomField): boolean {
	return ccField.type.toLowerCase().trim() === "boolean";
}

/**
 * Validate field definition for creation
 *
 * @param fieldName - Field name to validate
 * @param fieldType - Field type to validate
 * @param requestId - Request ID for logging
 * @returns true if field definition is valid
 */
export function validateFieldDefinition(
	fieldName: string,
	fieldType: string,
	requestId: string,
): boolean {
	// Validate field name
	if (!fieldName || fieldName.trim().length === 0) {
		logWarn(requestId, "Field name cannot be empty");
		return false;
	}
	
	// Validate field type
	if (!fieldType || fieldType.trim().length === 0) {
		logWarn(requestId, `Field type cannot be empty for field "${fieldName}"`);
		return false;
	}
	
	// Check if field name contains invalid characters
	const invalidChars = /[<>:"\/\\|?*]/;
	if (invalidChars.test(fieldName)) {
		logWarn(requestId, `Field name "${fieldName}" contains invalid characters`);
		return false;
	}
	
	logDebug(requestId, `Field definition validated: "${fieldName}" (${fieldType})`);
	return true;
}
